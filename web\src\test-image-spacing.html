<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片间距测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            line-height: 1.6;
        }
        
        .test-container {
            border: 1px solid #ccc;
            padding: 20px;
            margin: 20px 0;
            background: #f9f9f9;
        }
        
        .cursor-test {
            border: 2px dashed #007bff;
            padding: 10px;
            margin: 10px 0;
            min-height: 50px;
            position: relative;
        }
        
        .cursor-test::before {
            content: "光标测试区域 - 点击图片前后查看光标位置";
            position: absolute;
            top: -25px;
            left: 0;
            font-size: 12px;
            color: #007bff;
            background: white;
            padding: 2px 5px;
        }
        
        /* 模拟ProseMirror样式 */
        .ProseMirror.ProseMirror {
            outline: none;
            padding: 10px;
            border: 1px solid #ddd;
            min-height: 100px;
        }
        
        .ProseMirror.ProseMirror p {
            margin: 0.25rem 0.5rem;
            position: relative;
            line-height: 1.5;
            min-height: 1.5em;
        }
        
        /* 应用我们的图片样式 */
        .ProseMirror.ProseMirror p > img,
        .ProseMirror.ProseMirror p > .resizable-image-wrapper {
            max-width: 80%;
            min-width: 5rem;
            margin: 0;
            padding: 0 0.5rem;
            display: inline-block;
            width: auto;
            border-radius: 0.25rem;
            position: relative;
            text-align: center;
            vertical-align: middle;
            transform: translateY(0);
            transition: none;
            line-height: normal;
            will-change: transform;
            white-space: nowrap;
            font-size: inherit;
            box-sizing: border-box;
        }
        
        .test-image {
            width: 100px;
            height: 60px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 4px;
            display: inline-block;
        }
        
        .spacing-indicator {
            background: rgba(255, 0, 0, 0.2);
            border: 1px dashed red;
            display: inline-block;
            width: 0.5rem;
            height: 20px;
            vertical-align: middle;
        }
    </style>
</head>
<body>
    <h1>图片光标间距测试</h1>
    
    <div class="test-container">
        <h3>测试1: 普通段落中的图片</h3>
        <div class="ProseMirror ProseMirror">
            <p>
                这是一段文字
                <span class="spacing-indicator" title="左间距"></span>
                <div class="test-image"></div>
                <span class="spacing-indicator" title="右间距"></span>
                这是图片后的文字
            </p>
        </div>
    </div>
    
    <div class="test-container">
        <h3>测试2: 应用padding样式的图片</h3>
        <div class="ProseMirror ProseMirror">
            <p>
                这是一段文字
                <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjYwIiB2aWV3Qm94PSIwIDAgMTAwIDYwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjNEVDREM0Ii8+Cjx0ZXh0IHg9IjUwIiB5PSIzNSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEyIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+VGVzdCBJbWFnZTwvdGV4dD4KPHN2Zz4K" alt="测试图片" />
                这是图片后的文字
            </p>
        </div>
    </div>
    
    <div class="test-container">
        <h3>测试3: 可编辑区域</h3>
        <div class="cursor-test" contenteditable="true">
            点击这里输入文字，然后在图片前后放置光标测试间距：
            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjYwIiB2aWV3Qm94PSIwIDAgMTAwIDYwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPgo8cmVjdCB3aWR0aD0iMTAwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjRkY2QjZCIi8+Cjx0ZXh0IHg9IjUwIiB5PSIzNSIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEyIiBmaWxsPSJ3aGl0ZSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+RWRpdGFibGU8L3RleHQ+Cjwvc3ZnPgo=" alt="可编辑测试图片" style="padding: 0 0.5rem; box-sizing: border-box;" />
            继续输入文字测试
        </div>
    </div>
    
    <div class="test-container">
        <h3>说明</h3>
        <ul>
            <li>红色虚线框表示左右间距区域</li>
            <li>图片应该与前后文字有适当的间距</li>
            <li>光标应该不会与图片重叠</li>
            <li>在可编辑区域中点击图片前后测试光标位置</li>
        </ul>
    </div>
    
    <script>
        // 添加一些交互来测试光标位置
        document.addEventListener('DOMContentLoaded', function() {
            const editableArea = document.querySelector('.cursor-test');
            
            editableArea.addEventListener('click', function(e) {
                console.log('点击位置:', e.target.tagName, e.offsetX, e.offsetY);
            });
            
            // 监听光标位置变化
            editableArea.addEventListener('selectionchange', function() {
                const selection = window.getSelection();
                if (selection.rangeCount > 0) {
                    const range = selection.getRangeAt(0);
                    console.log('光标位置:', range.startOffset, range.endOffset);
                }
            });
        });
    </script>
</body>
</html>
