// 图片样式 - 使用高优先级选择器替代 !important
.ProseMirror.ProseMirror {
  p {

    >img,
    >.resizable-image-wrapper {
      max-width: 80%;
      min-width: 5rem;
      margin: 0;
      /* 添加右边距，避免光标与图片重叠 */
      padding: 0;
      display: inline-block;
      /* 改为 inline-block，避免光标定位问题 */
      width: auto;
      border-radius: 0.25rem;
      position: relative;
      text-align: center;
      vertical-align: middle;
      /* 改为 middle，改善光标对齐 */
      transform: translateY(0);
      transition: none;
      line-height: normal;
      /* 恢复正常行高，避免光标定位异常 */
      will-change: transform;
      white-space: nowrap;
      font-size: inherit;
      /* 继承字体大小，避免光标高度异常 */

      img {
        height: auto;
        border-radius: 0.25rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 10%);
        position: relative;
        z-index: 1;
        display: block;
        margin: 0;
        padding: 0;
        transform: translateY(0);
        transition: none;
        will-change: transform;
        line-height: normal;
        /* 恢复正常行高 */
        font-size: inherit;
        /* 继承字体大小 */

        &:hover {
          box-shadow: 0 4px 8px rgba(0, 0, 0, 15%);
        }
      }

      &.ProseMirror-selectednode {
        outline: 3px solid var(--blue);
        outline-offset: 0;
        border: none;
        box-shadow:
          0 0 0 1px rgba(45, 140, 240, 50%),
          0 0 12px rgba(45, 140, 240, 30%);
        z-index: 2;
      }

      .resize-handle {
        display: none;
        opacity: 0;
        transition:
          opacity 0.2s ease,
          transform 0.2s ease;
      }

      &.ProseMirror-selectednode .resize-handle {
        display: block;
        opacity: 1;
        animation: handle-fade-in 0.2s ease forwards;
      }

      @keyframes handle-fade-in {
        from {
          opacity: 0;
          transform: scale(0.8);
        }

        to {
          opacity: 1;
          transform: scale(1);
        }
      }

      // 小屏幕设备的响应式布局优化
      @media (width <=768px) {
        max-width: 100%;
        min-width: unset;

        img {
          max-width: 100%;
          width: auto;
          object-fit: contain;
        }
      }
    }

    &:only-child {

      >img,
      >.resizable-image-wrapper {
        position: relative;
        display: inline-block;

        &.ProseMirror-selectednode {
          outline: 2px solid #2d8cf0;

          .resize-handle {
            display: block;
            position: absolute;
            z-index: 200;
          }
        }
      }
    }
  }

  // 列表中的图片样式
  ul,
  ol {
    li {
      p {

        img,
        .resizable-image-wrapper {
          max-width: 100%;
          min-width: 5rem;
          margin: 0;
          display: inline-block;
          border-radius: 0.25rem;
          vertical-align: middle;
          position: relative;
          transition:
            outline 0.2s ease,
            box-shadow 0.2s ease;

          &.ProseMirror-selectednode {
            outline: 2px solid #2d8cf0;
            outline-offset: 0;
            box-shadow:
              0 0 0 1px rgba(45, 140, 240, 50%),
              0 0 8px rgba(45, 140, 240, 30%);
            transform: translateZ(0);

            .resize-handle {
              display: block;
              position: absolute;
              z-index: 200;
              animation: handle-appear 0.2s ease forwards;
            }

            @keyframes handle-appear {
              from {
                opacity: 0;
                transform: scale(0.8);
              }

              to {
                opacity: 1;
                transform: scale(1);
              }
            }
          }
        }
      }
    }
  }

  // 任务列表中的图片样式
  ul[data-type='taskList'] {
    li {
      >div {

        img,
        .resizable-image-wrapper {
          max-width: 100%;
          min-width: 5rem;
          margin: 0;
          display: inline-block;
          border-radius: 0.25rem;
          vertical-align: middle;
          position: relative;
          transition: all 0.2s ease;

          &.ProseMirror-selectednode {
            outline: 2px solid #2d8cf0;
            outline-offset: 0;
            box-shadow:
              0 0 0 1px rgba(45, 140, 240, 50%),
              0 0 8px rgba(45, 140, 240, 30%);
            transform: translateZ(0);

            .resize-handle {
              display: block;
              position: absolute;
              z-index: 200;
              animation: resize-handle-in 0.2s ease forwards;
            }

            @keyframes resize-handle-in {
              from {
                opacity: 0;
                transform: scale(0.8);
              }

              to {
                opacity: 1;
                transform: scale(1);
              }
            }
          }
        }
      }
    }
  }

  // 处理图片后的 ProseMirror 分隔符和尾随换行符
  .ProseMirror-separator,
  .ProseMirror-trailingBreak {
    display: none;
    /* 隐藏这些元素，避免光标闪烁 */
    visibility: hidden;
    height: 0;
    width: 0;
    margin: 0;
    padding: 0;
    line-height: 0;
    font-size: 0;
  }

  // 图片后方的光标定位优化
  img+.ProseMirror-separator,
  .resizable-image-wrapper+.ProseMirror-separator,
  img+.ProseMirror-trailingBreak,
  .resizable-image-wrapper+.ProseMirror-trailingBreak {
    display: none;
  }
}

// 只读模式下的图片样式

/* 只读模式下的图片样式 - 需要强制覆盖编辑模式样式，保留必要的 !important */
.editor-readonly.editor-readonly {

  .ProseMirrorInput,
  .ProseMirror.ProseMirror {

    /* 确保所有图片容器没有边框和轮廓 */
    .resizable-image-wrapper,
    .resizable-image-wrapper.ProseMirror-selectednode,
    .resizable-image-wrapper[class*='ProseMirror'],
    div>.resizable-image-wrapper,
    p>.resizable-image-wrapper,
    li>.resizable-image-wrapper {
      outline: none !important;
      border: none !important;
      box-shadow: none !important;
      cursor: pointer;

      /* 确保调整大小的控制点不可见 */
      .resize-handle,
      .resize-handle[class*='handle-'] {
        display: none !important;
        visibility: hidden !important;
        pointer-events: none !important;
        opacity: 0 !important;
      }

      /* 悬停效果 - 已读状态下取消阴影效果 */
      &:hover {
        outline: none !important;
        border: none !important;
        box-shadow: none !important;
      }

      /* 图片元素样式 */
      img,
      img.ProseMirror-selectednode,
      img[class*='ProseMirror'] {
        outline: none !important;
        border: none !important;
        box-shadow: none !important;
        cursor: pointer;

        &:hover,
        &:focus,
        &:active {
          outline: none !important;
          border: none !important;
          box-shadow: none !important;
        }
      }
    }

    /* 直接添加到编辑器内容的图片 - 覆盖编辑模式下的悬浮效果 */
    >img,
    p>img,
    li>img,
    div>img,
    img.ProseMirror-selectednode,
    img[class*='ProseMirror'] {
      outline: none !important;
      border: none !important;
      box-shadow: none !important;
      cursor: pointer;

      &:hover,
      &:focus,
      &:active {
        outline: none !important;
        border: none !important;
        box-shadow: none !important;
      }
    }

    /* 特别针对段落中的图片，覆盖编辑模式的悬浮效果 */
    p>img:hover,
    p>.resizable-image-wrapper img:hover {
      box-shadow: none !important;
    }
  }
}

// 图片预览模态框样式
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 85%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  cursor: zoom-out;
  transition:
    opacity 0.3s ease,
    visibility 0.3s ease;
  padding: 2rem;
  box-sizing: border-box;
  backdrop-filter: blur(3px);
}

.modal-overlay-active {
  opacity: 1;
  visibility: visible;
}

.modal-overlay img {
  max-width: 95%;
  max-height: 90vh;
  object-fit: contain;
  border-radius: 0.25rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 25%);
  transform: scale(0.95);
  opacity: 0;
  transition:
    opacity 0.5s ease,
    transform 0.3s ease;
}

.modal-overlay-active img {
  opacity: 1;
  transform: scale(1);
}

.loading-spinner {
  border: 0.25rem solid rgba(255, 255, 255, 20%);
  border-top: 0.25rem solid #f0f0f0;
  border-radius: 50%;
  width: 3rem;
  height: 3rem;
  animation: spin 1s linear infinite;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@keyframes spin {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }

  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

// 针对移动设备的优化 - 移除控制点相关样式，统一使用桌面样式
@media (width <=768px) {
  .ProseMirror.ProseMirror {
    // 控制点样式已统一，不再需要移动设备特殊处理

    .resizable-image-wrapper.ProseMirror-selectednode {
      outline-width: 2px;
    }

    p,
    li,
    div {

      >img,
      >.resizable-image-wrapper {
        max-width: 100%;
        min-width: unset;
        margin: 0;
        padding: 0;

        img {
          max-width: 100%;
          margin: 0;
          padding: 0;
          width: auto;
          object-fit: contain;
        }
      }
    }
  }

  .modal-overlay {
    padding: 1rem;
  }

  .modal-overlay img {
    max-width: 100%;
  }

  .loading-spinner {
    width: 2.5rem;
    height: 2.5rem;
  }
}