// Vue 版本的图片节点视图样式
// 这些样式专门用于 ImageNodeView.vue 组件

.resizable-image-wrapper {
  position: relative;
  display: inline-block;
  max-width: 100%;
  box-sizing: border-box;
  margin: 0 0.25rem !important;
  /* 添加左右边距，避免光标与图片重叠，与主样式保持一致 */

  &.resizing {
    user-select: none;

    img {
      pointer-events: none;
    }
  }

  // 调整大小控制点样式 - 统一使用桌面设备规格
  .resize-handle {
    position: absolute;
    background: #2d8cf0;
    border: 1px solid white;
    border-radius: 50%;
    width: 8px;
    height: 8px;
    cursor: pointer;
    z-index: 100;
    opacity: 0;
    transition: opacity 0.2s ease;

    // 角控制点 - 相对于图片内容定位，考虑边距偏移
    &.handle-top-left {
      top: -4px;
      left: calc(0.25rem - 4px);
      cursor: nw-resize;
    }

    &.handle-top-right {
      top: -4px;
      right: calc(0.25rem - 4px);
      cursor: ne-resize;
    }

    &.handle-bottom-left {
      bottom: -4px;
      left: calc(0.25rem - 4px);
      cursor: sw-resize;
    }

    &.handle-bottom-right {
      bottom: -4px;
      right: calc(0.25rem - 4px);
      cursor: se-resize;
    }

    // 边控制点 - 相对于图片内容定位，考虑边距偏移
    &.handle-top {
      top: -2px;
      left: calc(50% - 6px);
      cursor: n-resize;
      width: 12px;
      height: 4px;
      border-radius: 2px;
    }

    &.handle-right {
      right: calc(0.25rem - 2px);
      top: calc(50% - 6px);
      cursor: e-resize;
      width: 4px;
      height: 12px;
      border-radius: 2px;
    }

    &.handle-bottom {
      bottom: -2px;
      left: calc(50% - 6px);
      cursor: s-resize;
      width: 12px;
      height: 4px;
      border-radius: 2px;
    }

    &.handle-left {
      left: calc(0.25rem - 2px);
      top: calc(50% - 6px);
      cursor: w-resize;
      width: 4px;
      height: 12px;
      border-radius: 2px;
    }

    // 移动设备优化已移除，统一使用桌面设备样式
  }

  // 尺寸信息显示
  .resize-info {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 80%);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 101;
    pointer-events: none;
  }
}

// 强制图片边距样式
.image-with-margin,
.resizable-image-wrapper.image-with-margin {
  margin: 0 0.25rem !important;
}
